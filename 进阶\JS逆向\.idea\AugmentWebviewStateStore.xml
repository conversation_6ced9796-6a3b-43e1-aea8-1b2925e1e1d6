<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;52b06eff-149b-478b-bbfc-10efefe7aac3&quot;,&quot;conversations&quot;:{&quot;b96646d1-b587-476b-a9d7-e584f25bca2e&quot;:{&quot;id&quot;:&quot;b96646d1-b587-476b-a9d7-e584f25bca2e&quot;,&quot;createdAtIso&quot;:&quot;2025-07-28T08:26:39.288Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-28T08:26:39.288Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0},&quot;52b06eff-149b-478b-bbfc-10efefe7aac3&quot;:{&quot;id&quot;:&quot;52b06eff-149b-478b-bbfc-10efefe7aac3&quot;,&quot;createdAtIso&quot;:&quot;2025-07-28T08:26:39.455Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-28T08:26:39.455Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;64d9a39f-76f5-41fa-9efc-bc6371e93458&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>